<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PICA Email Templates - Texte Noir</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      margin: 20px; 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: rgba(255,255,255,0.1);
      border-radius: 20px;
      padding: 30px;
      backdrop-filter: blur(10px);
    }
    .header {
      text-align: center;
      margin-bottom: 40px;
      color: white;
    }
    .header h1 {
      font-size: 48px;
      margin: 0;
      text-shadow: 0 0 20px rgba(6,182,212,0.8);
      letter-spacing: 6px;
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
      gap: 30px;
      margin-top: 30px;
    }
    .email-preview { 
      background: rgba(255,255,255,0.95); 
      border-radius: 15px; 
      padding: 25px; 
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      border: 2px solid rgba(6,182,212,0.3);
    }
    .email-title { 
      font-size: 20px; 
      font-weight: bold; 
      color: #8b5cf6; 
      margin-bottom: 20px; 
      border-bottom: 3px solid #8b5cf6; 
      padding-bottom: 10px;
      text-align: center;
      text-transform: uppercase;
      letter-spacing: 2px;
    }
    iframe { 
      width: 100%; 
      height: 600px; 
      border: 2px solid #06b6d4; 
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(6,182,212,0.3);
    }
    .status {
      background: rgba(16,185,129,0.2);
      border: 2px solid #10b981;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 30px;
      text-align: center;
    }
    .status h3 {
      color: #10b981;
      margin: 0 0 10px 0;
      font-size: 24px;
    }
    .status p {
      color: white;
      margin: 0;
      font-size: 16px;
    }
    .features {
      background: rgba(139,92,246,0.2);
      border: 2px solid #8b5cf6;
      border-radius: 15px;
      padding: 25px;
      margin-bottom: 30px;
      color: white;
    }
    .features h3 {
      color: #06b6d4;
      margin-top: 0;
      font-size: 22px;
    }
    .features ul {
      list-style: none;
      padding-left: 0;
    }
    .features li {
      position: relative;
      padding-left: 30px;
      margin-bottom: 10px;
      font-size: 16px;
    }
    .features li:before {
      content: "✅";
      position: absolute;
      left: 0;
      color: #10b981;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🛡️ PICA</h1>
      <p style="font-size: 18px; margin: 10px 0; opacity: 0.9; letter-spacing: 3px;">EMAIL TEMPLATES - TEXTE NOIR</p>
    </div>

    <div class="status">
      <h3>✅ CORRECTION APPLIQUÉE</h3>
      <p>Tous les templates d'email ont été mis à jour avec du texte en noir (#000000) pour une meilleure lisibilité</p>
    </div>

    <div class="features">
      <h3>🔧 Modifications Effectuées</h3>
      <ul>
        <li>Template de base (email_base.html) : texte principal en noir</li>
        <li>Confirmation d'email : tous les paragraphes en noir</li>
        <li>Réinitialisation de mot de passe : texte principal en noir</li>
        <li>Vérification OTP : contenu principal en noir</li>
        <li>Notification d'incident : texte des détails en noir</li>
        <li>Template simple : texte principal en noir</li>
        <li>Template Matrix : fond blanc avec texte noir pour le contenu</li>
      </ul>
    </div>

    <div class="grid">
      <!-- Password Reset -->
      <div class="email-preview">
        <div class="email-title">🔄 PASSWORD RESET</div>
        <iframe src="test_password_reset.html"></iframe>
      </div>

      <!-- Email Confirmation -->
      <div class="email-preview">
        <div class="email-title">✅ EMAIL CONFIRMATION</div>
        <iframe src="test_email_confirmation.html"></iframe>
      </div>

      <!-- OTP Verification -->
      <div class="email-preview">
        <div class="email-title">🔐 OTP VERIFICATION</div>
        <iframe src="test_otp_verification.html"></iframe>
      </div>

      <!-- Incident Notification -->
      <div class="email-preview">
        <div class="email-title">🚨 INCIDENT NOTIFICATION</div>
        <iframe src="test_incident_notification.html"></iframe>
      </div>
    </div>

    <div style="text-align: center; margin-top: 40px; color: white;">
      <p style="font-size: 18px; opacity: 0.9;">
        📧 Tous les emails utilisent maintenant du texte noir pour une meilleure lisibilité<br>
        🎨 Les éléments décoratifs conservent leurs couleurs d'origine<br>
        ✅ Compatible avec tous les clients email (Gmail, Outlook, etc.)
      </p>
    </div>
  </div>
</body>
</html>
